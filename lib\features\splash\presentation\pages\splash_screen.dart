import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_demo/core/config/injection/injection.dart';
import 'package:flutter_demo/core/router/app_navigator.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_bloc.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_event.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_state.dart';

/// 启动页面
///
/// 显示应用启动画面，处理初始化逻辑
class SplashScreen extends StatelessWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SplashBloc>()..add(InitializeAppEvent()),
      child: const SplashView(),
    );
  }
}

/// 启动页视图
///
/// 启动页面的内容部分
class SplashView extends StatelessWidget {
  const SplashView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 监听状态变化
    return BlocListener<SplashBloc, SplashState>(
      listener: (context, state) {
        if (state is AuthenticatedState) {
          // 已登录，导航到首页
          AppNavigator.goToHome(context);
        } else if (state is UnauthenticatedState) {
          // 未登录，导航到登录页面
          AppNavigator.goToLogin(context);
        } else if (state is InitializationFailureState) {
          Logger.info('splash','应用初始化失败: ${state.message}');
          AppNavigator.goToLogin(context);
        }
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/splash_bg.png'),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }
}
